<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { useToast } from "primevue/usetoast";
import type { IncomeAdjustItem, IncomeAdjustAccountRequest } from "../../types/adjustDetail";
import { incomeAdjustAccount } from "../../services/adjustDetail";
import { optionLoaders } from "../../utils/options";

const props = defineProps<{
  visible: boolean;
  incomeAdjustItem: IncomeAdjustItem | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "success"): void;
}>();

const toast = useToast();
const drawerVisible = ref(false);
const loading = ref(false);
const submitted = ref(false);

// 表单数据
const formData = ref<IncomeAdjustAccountRequest>({
  order_no: "",
  sub_order_no: "",
  charge_month: 0,
  adjust_month: 0,
  adjust_amount: 0,
  adjust_tax: 0,
  adjust_reason_class: "",
  adjust_reason: "",
});

// 日期选择器的值
const chargeMonthDate = ref<Date | null>(null);
const adjustMonthDate = ref<Date | null>(null);

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    drawerVisible.value = newVal;
    if (newVal && props.incomeAdjustItem) {
      initFormData();
    }
  }
);

// 监听内部状态变化
watch(drawerVisible, (newVal) => {
  emit("update:visible", newVal);
  if (!newVal) {
    resetForm();
  }
});

// 初始化表单数据
const initFormData = () => {
  if (props.incomeAdjustItem) {
    const item = props.incomeAdjustItem;
    
    formData.value = {
      order_no: item.order_num,
      sub_order_no: item.total_num,
      charge_month: 0,
      adjust_month: 0,
      adjust_amount: 0,
      adjust_tax: item.tax_rate || 0,
      adjust_reason_class: "",
      adjust_reason: "",
    };
  }
};

// 重置表单
const resetForm = () => {
  formData.value = {
    order_no: "",
    sub_order_no: "",
    charge_month: 0,
    adjust_month: 0,
    adjust_amount: 0,
    adjust_tax: 0,
    adjust_reason_class: "",
    adjust_reason: "",
  };
  chargeMonthDate.value = null;
  adjustMonthDate.value = null;
  submitted.value = false;
};

// 监听权责月份日期选择器变化
watch(chargeMonthDate, (newDate) => {
  if (newDate) {
    const year = newDate.getFullYear().toString().slice(-2);
    const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
    formData.value.charge_month = parseInt(`${year}${month}`);
  }
});

// 监听调账月份日期选择器变化
watch(adjustMonthDate, (newDate) => {
  if (newDate) {
    const year = newDate.getFullYear().toString().slice(-2);
    const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
    formData.value.adjust_month = parseInt(`${year}${month}`);
  }
});

// 加载调账原因分类选项
const adjustReasonClassOptions = ref<{ label: string; value: string }[]>([]);
const loadAdjustReasonClassOptions = async () =>
  optionLoaders.adjustReasonClass(adjustReasonClassOptions);

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
};

// 表单验证
const validateForm = (): boolean => {
  if (!formData.value.charge_month) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请选择权责月份",
      life: 3000,
    });
    return false;
  }
  
  if (!formData.value.adjust_month) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请选择调账月份",
      life: 3000,
    });
    return false;
  }
  
  if (!formData.value.adjust_amount || formData.value.adjust_amount === 0) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请输入调账金额",
      life: 3000,
    });
    return false;
  }
  
  if (!formData.value.adjust_reason_class) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请选择调账原因分类",
      life: 3000,
    });
    return false;
  }
  
  if (!formData.value.adjust_reason) {
    toast.add({
      severity: "error",
      summary: "验证错误",
      detail: "请输入调账原因",
      life: 3000,
    });
    return false;
  }

  return true;
};

// 提交表单
const submitForm = async () => {
  submitted.value = true;
  
  if (!validateForm()) {
    return;
  }

  try {
    loading.value = true;
    
    await incomeAdjustAccount(formData.value);
    
    toast.add({
      severity: "success",
      summary: "成功",
      detail: "无权责调账操作成功",
      life: 3000,
    });
    
    emit("success");
    closeDrawer();
  } catch (error: any) {
    // 处理422错误
    if (error.response?.status === 422) {
      const errorMessage = error.response?.data?.message || "数据验证失败";
      toast.add({
        severity: "error",
        summary: "验证错误",
        detail: errorMessage,
        life: 5000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: error.response?.data?.message || "无权责调账操作失败",
        life: 3000,
      });
    }
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadAdjustReasonClassOptions();
});
</script>

<template>
  <Drawer
    v-model:visible="drawerVisible"
    :header="`无权责调账 - ${incomeAdjustItem?.order_num || ''}`"
    position="right"
    class="income-adjust-drawer"
    :style="{ width: '50rem' }"
    :modal="true"
    :dismissableMask="true"
    @hide="closeDrawer"
  >
    <div v-if="incomeAdjustItem" class="drawer-content">
      <!-- 订单信息展示 -->
      <div class="order-info-section">
        <h3 class="section-title">
          <i class="pi pi-info-circle"></i>
          订单信息
        </h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="info-item">
            <label>订单编号</label>
            <span class="info-value">{{ incomeAdjustItem.order_num }}</span>
          </div>
          <div class="info-item">
            <label>合成编号</label>
            <span class="info-value">{{ incomeAdjustItem.total_num }}</span>
          </div>
          <div class="info-item">
            <label>税率</label>
            <span class="info-value">{{ incomeAdjustItem.tax_rate || 0 }}%</span>
          </div>
        </div>
      </div>

      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基本信息</h3>
          <Divider />
        </div>
        <div class="section-content">
          <Fluid>
            <div class="grid grid-cols-3 gap-4">
              <div class="field">
                <label for="batch_no">批次号</label>
                <InputText
                  v-model="formData.batch_no"
                  disabled
                  class="w-full"
                />
              </div>
              <div class="field">
                <label for="order_no">订单编号</label>
                <InputText
                  v-model="formData.order_no"
                  disabled
                  class="w-full"
                />
              </div>
              <div class="field">
                <label for="sub_order_no">子订单编号</label>
                <InputText
                  v-model="formData.sub_order_no"
                  disabled
                  class="w-full"
                />
              </div>
              <div class="field">
                <label for="adjust_month">调整账期</label>
                <InputNumber
                  v-model="formData.adjust_month"
                  disabled
                  :useGrouping="false"
                  class="w-full"
                />
              </div>
              <div class="field">
                <label for="adjust_tax">调账税率</label>
                <InputNumber
                  v-model="formData.adjust_tax"
                  disabled
                  suffix="%"
                  :maxFractionDigits="2"
                  class="w-full"
                />
              </div>
            </div>
          </Fluid>
        </div>
      </div>

      <!-- 调账表单 -->
      <div class="adjust-form-section">
        <h3 class="section-title">
          <i class="pi pi-edit"></i>
          调账信息
        </h3>
        <form @submit.prevent="submitForm">
          <Fluid>
            <div class="grid grid-cols-2 gap-4">
              <div class="form-field">
                <FloatLabel>
                  <DatePicker
                    v-model="chargeMonthDate"
                    view="month"
                    dateFormat="yymm"
                    showIcon
                    class="w-full"
                    :class="{ 'p-invalid': submitted && !formData.charge_month }"
                  />
                  <label>权责月份 *</label>
                </FloatLabel>
              </div>
              <div class="form-field">
                <FloatLabel>
                  <DatePicker
                    v-model="adjustMonthDate"
                    view="month"
                    dateFormat="yymm"
                    showIcon
                    locale="zh-CN"
                    class="w-full"
                    :class="{ 'p-invalid': submitted && !formData.adjust_month }"
                  />
                  <label>调账月份 *</label>
                </FloatLabel>
              </div>
              <div class="form-field">
                <FloatLabel>
                  <InputNumber
                    v-model="formData.adjust_amount"
                    :maxFractionDigits="2"
                    class="w-full"
                    :class="{ 'p-invalid': submitted && (!formData.adjust_amount || formData.adjust_amount === 0) }"
                  />
                  <label>调账金额 *</label>
                </FloatLabel>
              </div>
              <div class="form-field col-span-2">
                <FloatLabel>
                  <Select
                    v-model="formData.adjust_reason_class"
                    :options="adjustReasonClassOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="请选择调账原因分类"
                    class="w-full"
                    :class="{ 'p-invalid': submitted && !formData.adjust_reason_class }"
                  />
                  <label>调账原因分类 *</label>
                </FloatLabel>
              </div>
            </div>
            <div class="form-field mt-4">
              <FloatLabel>
                <Textarea
                  v-model="formData.adjust_reason"
                  rows="3"
                  class="w-full"
                  :class="{ 'p-invalid': submitted && !formData.adjust_reason }"
                />
                <label>调账原因 *</label>
              </FloatLabel>
            </div>
          </Fluid>
        </form>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <Button
          label="取消"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="closeDrawer"
          class="apple-button mr-2"
        />
        <Button
          label="确认调账"
          icon="pi pi-check"
          @click="submitForm"
          :loading="loading"
          class="apple-button"
        />
      </div>
    </template>
  </Drawer>
</template>

<style scoped>
/* Apple Design System - 无权责调账抽屉样式 */
:deep(.income-adjust-drawer) {
  border-radius: 16px 0 0 16px !important;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1) !important;
}

:deep(.income-adjust-drawer .p-drawer-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
}

:deep(.income-adjust-drawer .p-drawer-header .p-drawer-title) {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  color: #1d1d1f !important;
}

:deep(.income-adjust-drawer .p-drawer-content) {
  padding: 0 !important;
  background: #ffffff !important;
}

.drawer-content {
  padding: 2rem;
  height: 100%;
  overflow-y: auto;
}

.order-info-section,
.adjust-form-section {
  margin-bottom: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #007aff;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

.info-value {
  font-size: 1rem;
  font-weight: 500;
  color: #1d1d1f;
  padding: 0.5rem 0.75rem;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.form-field {
  margin-bottom: 1.5rem;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background: #f8f9fa;
}

.apple-button {
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.apple-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表单验证样式 */
:deep(.p-invalid) {
  border-color: #ef4444 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 0.2rem rgba(239, 68, 68, 0.2) !important;
}
</style>
