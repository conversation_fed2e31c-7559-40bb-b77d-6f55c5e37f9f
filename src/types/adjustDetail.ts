// 调账记录相关类型定义

export interface AdjustDetailItem {
  id: number;
  batch_no: string;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  adjust_month: number;
  adjust_amount: number;
  adjust_tax: number;
  adjust_reason_class: string;
  adjust_reason: string;
  state: number;
  adjust_charge_detail_id: number | null;
  adjust_type: number | null;
  create_user: string | null;
  created_at: string;
}

export interface AdjustDetailParams {
  page?: number;
  pageSize?: number;
  adjust_month?: string;
  adjust_type?: number;
  charge_month?: string;
  order_no?: string;
  [key: string]: any;
}

// 权责调账相关类型定义
export interface ChargeAdjustItem {
  id: number;
  batch_no: string;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  fee_amount: number;
  income_type: string;
  tax_type: string;
  customer_name: string;
}

export interface ChargeAdjustParams {
  page?: number;
  pageSize?: number;
  charge_month?: string;
  order_no?: string;
  [key: string]: any;
}
