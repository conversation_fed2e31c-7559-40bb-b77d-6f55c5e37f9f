# 权责调账功能实现说明

## 功能概述
在权责调账的Tab中增加了完整的列表功能，包括数据获取、筛选、分页和操作按钮。

## 实现的功能

### 1. 数据接口
- **API端点**: `/charge-adjust`
- **支持参数**: 
  - `charge_month`: 账期 (格式: YYMM)
  - `order_no`: 订单编号
  - `page`: 页码
  - `pageSize`: 每页数量

### 2. 数据结构
根据后端返回的数据格式，定义了 `ChargeAdjustItem` 类型：
```typescript
interface ChargeAdjustItem {
  id: number;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  fee_amount: number;
  income_type: string;
  tax_type: string;
  customer_name: string;
}
```

### 3. 筛选功能
- **订单编号筛选**: 支持输入订单编号进行精确搜索
- **账期筛选**: 使用日历控件，格式为YYMM，支持按月份筛选
- **搜索和重置**: 提供搜索按钮和重置按钮

### 4. 列表展示
包含以下列：
- 订单编号
- 子订单编号  
- 账期 (格式化显示为 YYYY-MM)
- 费用金额 (格式化为货币显示)
- 收入类型
- 税收类型
- 客户名称
- 操作按钮

### 5. 分页功能
- 默认每页显示10条记录
- 支持10、20、50条记录切换
- 显示总记录数
- 支持页码跳转

### 6. 操作功能
- **调账按钮**: 每行都有调账操作按钮
- **权限控制**: 根据用户权限控制按钮是否可用
- **操作反馈**: 点击后显示提示信息

## 技术实现

### 1. 类型定义 (src/types/adjustDetail.ts)
```typescript
// 新增权责调账相关类型
export interface ChargeAdjustItem {
  id: number;
  order_no: string;
  sub_order_no: string;
  charge_month: number;
  fee_amount: number;
  income_type: string;
  tax_type: string;
  customer_name: string;
}

export interface ChargeAdjustParams {
  page?: number;
  pageSize?: number;
  charge_month?: string;
  order_no?: string;
  [key: string]: any;
}
```

### 2. API服务 (src/services/adjustDetail.ts)
```typescript
// 新增权责调账API
export const getChargeAdjustList = async (
  params: ChargeAdjustParams
): Promise<ApiListResponse<ChargeAdjustItem[]>> => {
  const response = await api.get("/charge-adjust", { params });
  return response.data;
};
```

### 3. 组件实现 (src/views/account/Adjust.vue)
- 新增权责调账相关状态管理
- 实现数据加载、筛选、分页逻辑
- 添加完整的UI界面
- 集成权限控制

## 界面特性

### 1. 响应式设计
- 支持不同屏幕尺寸
- 移动端友好的布局

### 2. 用户体验
- 加载状态指示
- 空数据提示
- 错误处理和提示
- 统一的设计风格

### 3. 数据格式化
- 账期格式化 (202506 → 2025-06)
- 金额格式化 (29.68 → ¥29.68)
- 时间格式化

## 使用说明

1. **切换到权责调账Tab**: 点击"权责调账"标签页
2. **筛选数据**: 
   - 输入订单编号进行搜索
   - 选择账期进行筛选
   - 点击"搜索"按钮执行筛选
   - 点击"重置"按钮清空筛选条件
3. **查看数据**: 在表格中查看权责调账列表
4. **分页操作**: 使用底部分页控件切换页面
5. **调账操作**: 点击操作列的"调账"按钮执行调账操作

## 后续扩展

1. **调账操作**: 当前调账按钮为占位实现，需要根据具体业务需求完善
2. **批量操作**: 可以添加批量调账功能
3. **导出功能**: 可以添加Excel导出功能
4. **高级筛选**: 可以添加更多筛选条件

## 注意事项

1. 确保后端API `/charge-adjust` 已经实现并返回正确的数据格式
2. 权限控制依赖于 `usePermission` 组合式函数
3. 日期格式化使用 `isoFormatYYmm` 工具函数
4. 错误处理使用 PrimeVue 的 Toast 组件显示提示信息
